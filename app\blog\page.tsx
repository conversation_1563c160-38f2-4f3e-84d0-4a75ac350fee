import Link from 'next/link';

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: 'DeepBI 2024 年度更新：全新功能发布',
      excerpt: '了解 DeepBI 最新版本中的重要功能更新，包括增强的数据可视化、改进的性能和全新的用户界面。',
      date: '2024-01-15',
      author: 'DeepBI 团队',
      category: '产品更新',
    },
    {
      id: 2,
      title: '如何构建高效的商业智能仪表板',
      excerpt: '学习设计和构建高效商业智能仪表板的最佳实践，包括数据选择、可视化技巧和用户体验优化。',
      date: '2024-01-10',
      author: '张三',
      category: '最佳实践',
    },
    {
      id: 3,
      title: '数据可视化的艺术：选择正确的图表类型',
      excerpt: '深入探讨不同数据类型应该使用哪种图表，以及如何通过正确的可视化方式传达数据洞察。',
      date: '2024-01-05',
      author: '李四',
      category: '教程',
    },
    {
      id: 4,
      title: 'DeepBI 与现代数据栈的集成',
      excerpt: '了解如何将 DeepBI 与您现有的数据基础设施集成，包括数据仓库、ETL 工具和云服务。',
      date: '2023-12-28',
      author: '王五',
      category: '技术',
    },
  ];

  return (
    <main className="flex flex-1 flex-col px-4 py-8">
      <div className="max-w-4xl mx-auto w-full">
        <div className="text-center mb-12">
          <h1 className="mb-4 text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            DeepBI 博客
          </h1>
          <p className="text-lg text-fd-muted-foreground">
            最新的产品更新、技术洞察和最佳实践
          </p>
        </div>

        <div className="grid gap-8">
          {blogPosts.map((post) => (
            <article key={post.id} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center gap-4 mb-3">
                <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">
                  {post.category}
                </span>
                <span className="text-sm text-fd-muted-foreground">
                  {post.date}
                </span>
              </div>
              
              <h2 className="text-2xl font-semibold mb-3 hover:text-blue-600 transition-colors">
                <Link href={`/blog/${post.id}`}>
                  {post.title}
                </Link>
              </h2>
              
              <p className="text-fd-muted-foreground mb-4 leading-relaxed">
                {post.excerpt}
              </p>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-fd-muted-foreground">
                  作者：{post.author}
                </span>
                <Link 
                  href={`/blog/${post.id}`}
                  className="text-blue-600 hover:underline font-medium"
                >
                  阅读更多 →
                </Link>
              </div>
            </article>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-fd-muted-foreground mb-4">
            想要了解更多关于 DeepBI 的信息？
          </p>
          <Link 
            href="/docs" 
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            查看文档
          </Link>
        </div>
      </div>
    </main>
  );
}
