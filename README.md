# Fumadocs 静态导出项目

这是一个配置了静态导出功能的 Fumadocs 文档站点。

## 功能特性

- ✅ 静态导出支持
- ✅ 静态搜索功能（使用 Orama）
- ✅ 响应式设计
- ✅ 主题切换
- ✅ MDX 支持

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 构建和部署

```bash
# 构建静态文件
npm run build

# 本地预览静态站点
npm run serve
```

构建完成后，静态文件将生成在 `out` 目录中，可以直接部署到任何静态托管服务。

## 项目结构

```text
my-app/
├── app/                    # Next.js App Router
├── components/             # 自定义组件
│   ├── provider.tsx       # 搜索 Provider
│   └── search.tsx         # 静态搜索组件
├── content/               # MDX 文档内容
├── lib/                   # 工具函数
├── out/                   # 构建输出目录
└── next.config.mjs        # Next.js 配置（已配置静态导出）
```

## 配置说明

### 静态导出配置

在 `next.config.mjs` 中已配置：

```javascript
const config = {
  reactStrictMode: true,
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
};
```

### 静态搜索配置

- 搜索 API 路由使用 `staticGET` 方法
- 自定义搜索组件使用 Orama 进行客户端搜索
- 支持英文搜索（可根据需要调整语言）

## 部署

生成的 `out` 目录可以部署到：

- GitHub Pages
- Netlify
- Vercel
- AWS S3
- 任何静态文件托管服务

## 添加内容

在 `content/docs/` 目录下添加 MDX 文件即可自动生成文档页面。
