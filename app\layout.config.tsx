import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';

/**
 * Shared layout configurations
 *
 * you can customise layouts individually from:
 * Home Layout: app/(home)/layout.tsx
 * Docs Layout: app/docs/layout.tsx
 */
export const baseOptions: BaseLayoutProps = {
  nav: {
    title: (
      <>
        <svg
          width="24"
          height="24"
          xmlns="http://www.w3.org/2000/svg"
          aria-label="DeepBI Logo"
          viewBox="0 0 24 24"
          fill="none"
        >
          <rect x="3" y="3" width="18" height="18" rx="2" fill="currentColor" opacity="0.1"/>
          <path d="M7 7h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z" fill="currentColor"/>
          <circle cx="19" cy="5" r="2" fill="#3F7BF4"/>
        </svg>
        <span className="font-semibold">DeepBI</span>
      </>
    ),

  },
  // 添加顶部导航链接
  links: [
    {
      text: 'Home',
      url: '/',
      active: 'nested-url',
    },
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
    {
      text: 'Showcase',
      url: '/showcase',
      external: false,
    },
    {
      text: 'Blog',
      url: '/blog',
      external: false,
    },
  ],
  // 主题切换器
  themeSwitch: {
    enabled: true,
    mode: 'light-dark-system',
  },
};
