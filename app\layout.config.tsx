import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';

/**
 * 外部链接配置 - 您可以在这里修改外部链接地址
 */
export const EXTERNAL_LINKS = {
  // 主页链接 - 如果需要跳转到外部网站，请修改此 URL
  HOME_URL: '/', // 例如: 'https://your-main-website.com'

  // 博客链接 - 如果需要跳转到外部博客，请修改此 URL
  BLOG_URL: '/blog', // 例如: 'https://blog.your-website.com'

  // 其他外部链接
  SHOWCASE_URL: '/showcase',
} as const;

/**
 * Shared layout configurations
 *
 * you can customise layouts individually from:
 * Home Layout: app/(home)/layout.tsx
 * Docs Layout: app/docs/layout.tsx
 */
export const baseOptions: BaseLayoutProps = {
  nav: {
    title: (
      <>
        <svg
          width="24"
          height="24"
          xmlns="http://www.w3.org/2000/svg"
          aria-label="DeepBI Logo"
          viewBox="0 0 24 24"
          fill="none"
        >
          <rect x="3" y="3" width="18" height="18" rx="2" fill="currentColor" opacity="0.1"/>
          <path d="M7 7h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z" fill="currentColor"/>
          <circle cx="19" cy="5" r="2" fill="#3F7BF4"/>
        </svg>
        <span className="font-semibold">DeepBI</span>
      </>
    ),

  },
  // 添加顶部导航链接
  links: [
    {
      text: 'Home',
      url: EXTERNAL_LINKS.HOME_URL,
      active: 'nested-url',
      // 如果 HOME_URL 是外部链接，请将下面的注释取消并设置为 true
      // external: EXTERNAL_LINKS.HOME_URL.startsWith('http'),
    },
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
    {
      text: 'Showcase',
      url: EXTERNAL_LINKS.SHOWCASE_URL,
      external: false,
    },
    {
      text: 'Blog',
      url: EXTERNAL_LINKS.BLOG_URL,
      // 如果 BLOG_URL 是外部链接，请将下面的注释取消并设置为 true
      // external: EXTERNAL_LINKS.BLOG_URL.startsWith('http'),
    },
  ],
  // 主题切换器
  themeSwitch: {
    enabled: true,
    mode: 'light-dark-system',
  },
};
