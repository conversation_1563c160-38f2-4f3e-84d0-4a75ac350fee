import Link from 'next/link';

export default function ShowcasePage() {
  return (
    <main className="flex flex-1 flex-col justify-center text-center px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="mb-8 text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          DeepBI Showcase
        </h1>
        <p className="text-lg text-fd-muted-foreground mb-12">
          探索 DeepBI 的强大功能和真实案例
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold mb-3">销售仪表板</h3>
            <p className="text-fd-muted-foreground mb-4">
              实时监控销售数据，包括收入趋势、转化率和客户分析
            </p>
            <div className="bg-gray-100 dark:bg-gray-800 h-32 rounded mb-4 flex items-center justify-center">
              <span className="text-fd-muted-foreground">仪表板预览</span>
            </div>
            <Link href="/docs" className="text-blue-600 hover:underline">
              查看详情 →
            </Link>
          </div>

          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold mb-3">财务报表</h3>
            <p className="text-fd-muted-foreground mb-4">
              自动化财务报表生成，支持多维度数据分析
            </p>
            <div className="bg-gray-100 dark:bg-gray-800 h-32 rounded mb-4 flex items-center justify-center">
              <span className="text-fd-muted-foreground">报表预览</span>
            </div>
            <Link href="/docs" className="text-blue-600 hover:underline">
              查看详情 →
            </Link>
          </div>

          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold mb-3">用户行为分析</h3>
            <p className="text-fd-muted-foreground mb-4">
              深入了解用户行为模式，优化产品体验
            </p>
            <div className="bg-gray-100 dark:bg-gray-800 h-32 rounded mb-4 flex items-center justify-center">
              <span className="text-fd-muted-foreground">分析预览</span>
            </div>
            <Link href="/docs" className="text-blue-600 hover:underline">
              查看详情 →
            </Link>
          </div>
        </div>

        <div className="text-center">
          <Link 
            href="/docs/getting-started" 
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            开始使用 DeepBI
          </Link>
        </div>
      </div>
    </main>
  );
}
