import { source } from '@/lib/source';
import { createFromSource } from 'fumadocs-core/search/server';

// 缓存搜索索引以支持静态导出
export const revalidate = false;

const searchHandler = createFromSource(source, {
  // https://docs.orama.com/docs/orama-js/supported-languages
  language: 'english',
});

// 在开发模式下使用 GET，在生产模式下使用 staticGET
export const GET = process.env.NODE_ENV === 'development'
  ? searchHandler.GET
  : searchHandler.staticGET;
