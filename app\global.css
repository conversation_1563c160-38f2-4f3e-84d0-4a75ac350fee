@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* 自定义选中项颜色 */
:root {
  --fd-primary: 63 123 244; /* #3F7BF4 */
}

/* 侧边栏导航选中项样式 */
[data-active="true"] {
  background-color: rgb(63 123 244 / 0.1) !important;
  color: rgb(63 123 244) !important;
  border-color: rgb(63 123 244) !important;
}

/* 侧边栏导航选中项悬停状态 */
[data-active="true"]:hover {
  background-color: rgb(63 123 244 / 0.15) !important;
}

/* 链接选中状态 */
.fd-sidebar a[data-active="true"] {
  background-color: rgb(63 123 244 / 0.1) !important;
  color: rgb(63 123 244) !important;
}

/* 目录（TOC）选中项 */
.fd-toc a[data-active="true"] {
  color: rgb(63 123 244) !important;
  border-left-color: rgb(63 123 244) !important;
}
