@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';
@import 'tw-animate-css';
@plugin "tailwindcss-motion";

@custom-variant dark (&:is(.dark *));

@source '../node_modules/fumadocs-ui/dist/**/*.js';


/* Animation delay utilities for sparkles effect */
.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-700 {
  animation-delay: 700ms;
}

:root {
  --radius: 0.625rem;

  --background: oklch(1 0 0);

  --foreground: oklch(0.145 0 0);

  --card: oklch(1 0 0);

  --card-foreground: oklch(0.145 0 0);

  --popover: oklch(1 0 0);

  --popover-foreground: oklch(0.145 0 0);

  --primary: oklch(0.205 0 0);

  --primary-foreground: oklch(0.985 0 0);

  --secondary: oklch(0.97 0 0);

  --secondary-foreground: oklch(0.205 0 0);

  --muted: oklch(0.97 0 0);

  --muted-foreground: oklch(0.556 0 0);

  --accent: oklch(0.97 0 0);

  --accent-foreground: oklch(0.205 0 0);

  --destructive: oklch(0.577 0.245 27.325);

  --border: oklch(0.922 0 0);

  --input: oklch(0.922 0 0);

  --ring: oklch(0.708 0 0);

  --chart-1: oklch(0.646 0.222 41.116);

  --chart-2: oklch(0.6 0.118 184.704);

  --chart-3: oklch(0.398 0.07 227.392);

  --chart-4: oklch(0.828 0.189 84.429);

  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);

  --sidebar-foreground: oklch(0.145 0 0);

  --sidebar-primary: oklch(0.205 0 0);

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.97 0 0);

  --sidebar-accent-foreground: oklch(0.205 0 0);

  --sidebar-border: oklch(0.922 0 0);

  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);

  --foreground: oklch(0.985 0 0);

  --card: oklch(0.205 0 0);

  --card-foreground: oklch(0.985 0 0);

  --popover: oklch(0.205 0 0);

  --popover-foreground: oklch(0.985 0 0);

  --primary: oklch(0.922 0 0);

  --primary-foreground: oklch(0.205 0 0);

  --secondary: oklch(0.269 0 0);

  --secondary-foreground: oklch(0.985 0 0);

  --muted: oklch(0.269 0 0);

  --muted-foreground: oklch(0.708 0 0);

  --accent: oklch(0.269 0 0);

  --accent-foreground: oklch(0.985 0 0);

  --destructive: oklch(0.704 0.191 22.216);

  --border: oklch(1 0 0 / 10%);

  --input: oklch(1 0 0 / 15%);

  --ring: oklch(0.556 0 0);

  --chart-1: oklch(0.488 0.243 264.376);

  --chart-2: oklch(0.696 0.17 162.48);

  --chart-3: oklch(0.769 0.188 70.08);

  --chart-4: oklch(0.627 0.265 303.9);

  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.205 0 0);

  --sidebar-foreground: oklch(0.985 0 0);

  --sidebar-primary: oklch(0.488 0.243 264.376);

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.269 0 0);

  --sidebar-accent-foreground: oklch(0.985 0 0);

  --sidebar-border: oklch(1 0 0 / 10%);

  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);

  --radius-md: calc(var(--radius) - 2px);

  --radius-lg: var(--radius);

  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);

  --color-foreground: var(--foreground);

  --color-card: var(--card);

  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);

  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);

  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);

  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);

  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);

  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);

  --color-border: var(--border);

  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);

  --color-chart-2: var(--chart-2);

  --color-chart-3: var(--chart-3);

  --color-chart-4: var(--chart-4);

  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);

  --color-sidebar-foreground: var(--sidebar-foreground);

  --color-sidebar-primary: var(--sidebar-primary);

  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);

  --color-sidebar-accent: var(--sidebar-accent);

  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);

  --color-sidebar-border: var(--sidebar-border);

  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme static {
  --transparent: transparent;
  --white: var(--color-white);
  --black: var(--color-black);
  --blue-300: var(--color-blue-300);
  --blue-400: var(--color-blue-400);
  --blue-500: var(--color-blue-500);
  --indigo-300: var(--color-indigo-300);
  --violet-200: var(--color-violet-200);
}

@theme {
  --animate-aurora: aurora 60s linear infinite;

  @keyframes aurora {
    from {
      background-position:
        50% 50%,
        50% 50%;
    }
    to {
      background-position:
        350% 50%,
        350% 50%;
    }
  }
}

/* 顶部导航栏样式优化 */
.fd-nav {
  border-bottom: 1px solid var(--color-border);
  backdrop-filter: blur(8px);
  background-color: rgba(var(--color-background), 0.8);
}

/* 搜索框在顶部导航栏的样式 */
[data-search-trigger] {
  min-width: 200px;
  background-color: var(--color-muted);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: var(--color-muted-foreground);
  transition: all 0.2s ease;
}

[data-search-trigger]:hover {
  background-color: var(--color-accent);
  border-color: var(--color-ring);
}

[data-search-trigger]:focus {
  outline: none;
  border-color: var(--color-ring);
  box-shadow: 0 0 0 2px rgba(var(--color-ring), 0.2);
}

/* 导航链接样式 */
.fd-nav a {
  color: var(--color-foreground);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.fd-nav a:hover {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}

.fd-nav a[data-active="true"] {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
}

/* 响应式导航 */
@media (max-width: 768px) {
  [data-search-trigger] {
    min-width: 150px;
  }
}
