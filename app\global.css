@import 'tailwindcss' source(none);
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';
@import 'fumadocs-twoslash/twoslash.css';
@import 'fumadocs-openapi/css/preset.css';

@source ".";
@source "../components";
@source "../content";
@plugin 'tailwindcss-animate';

@theme inline {
  --default-mono-font-family: var(--font-mono);
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  @keyframes stroke {
    from {
      stroke-dasharray: 1000;
    }
    to {
      stroke-dasharray: 1000;
      stroke-dashoffset: 2000;
    }
  }

  --animate-stroke: stroke 5s linear infinite;

  --color-gradient-radial: radial-gradient(circle, var(--tw-gradient-stops));
  --color-repeat-gradient-to-r: repeating-linear-gradient(
    to right,
    var(--tw-gradient-stops)
  );
  --color-repeat-gradient-to-br: repeating-linear-gradient(
    to bottom right,
    var(--tw-gradient-stops)
  );
}

.ui {
  --color-fd-primary: var(--ui-color);
}

.headless {
  --color-fd-primary: var(--headless-color);
}

:root {
  --headless-color: hsl(250, 80%, 54%);
  --ui-color: hsl(220, 91%, 54%);
  /* 自定义主色调为 #3F7BF4 */
  --color-fd-primary: 63 123 244;
}

body {
  overscroll-behavior-y: none;
  background-color: var(--color-fd-background);
}

.dark {
  --headless-color: hsl(250 100% 80%);
  --ui-color: hsl(217 92% 76%);
}

@keyframes circuit_1 {
  0% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(100px);
  }
}

@keyframes circuit_1_x_energy {
  0%,
  46% {
    opacity: 0;
    transform: translateX(-20px);
  }
  47%,
  70% {
    opacity: 100%;
  }
  100% {
    opacity: 0;
    transform: translateX(212px);
  }
}
